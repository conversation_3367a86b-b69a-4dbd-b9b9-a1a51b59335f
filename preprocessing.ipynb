{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import xml.etree.ElementTree as ET"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 元データ\n", "original_net_path = 'dataset/data.net.xml'\n", "original_sumocfg_path = 'dataset/data.sumocfg'\n", "# 最適化用に一部書き換えたデータ\n", "new_net_path = 'dataset/modified_data.net.xml'\n", "new_sumocfg_path = 'dataset/modified_data.sumocfg'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Load the XML file again to discard the previous changes\n", "tree = ET.parse(original_net_path)\n", "root = tree.getroot()\n", "\n", "# Iterate over all 'tlLogic' elements\n", "for tl_logic in root.findall('.//tlLogic'):\n", "    # Create a list to store new phases\n", "    new_phases = []\n", "\n", "    # Iterate over the phases and add to new_phases if 'y' is not in the state\n", "    for phase in tl_logic.findall('phase'):\n", "        if 'y' not in phase.attrib['state']:\n", "            new_phase = ET.Element('phase', attrib=phase.attrib)\n", "            new_phase.attrib['duration'] = '60'  # Set new duration\n", "            new_phases.append(new_phase)\n", "\n", "    # Clear all the phases in the current tlLogic\n", "    for phase in tl_logic.findall('phase'):\n", "        tl_logic.remove(phase)\n", "\n", "    # Add the new phases to tlLogic\n", "    for new_phase in new_phases:\n", "        tl_logic.append(new_phase)\n", "\n", "# Save the modified XML to a new file\n", "tree.write(new_net_path)\n", "\n", "\n", "# Load the sumocfg file\n", "tree_sumocfg = ET.parse(original_sumocfg_path)\n", "root_sumocfg = tree_sumocfg.getroot()\n", "\n", "# Find the 'net-file' element and replace its value\n", "net_file = root_sumocfg.find(\".//net-file\")\n", "net_file.attrib['value'] = os.path.basename(new_net_path)\n", "\n", "# Save the modified sumocfg to a new file\n", "tree_sumocfg.write(new_sumocfg_path)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "opt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}