import pandas as pd
from openjij import SASampler
import traci

from functions import *

num_instances=10

Tall = 400
interval=20
alpha, gamma = 1, 10
yellow_signal_time, lam = 2, 2

result= {}

# 元データ
original_net_path = 'dataset/data.net.xml'
original_sumocfg_path = 'dataset/data.sumocfg'
# 最適化用に一部書き換えたデータ
new_net_path = 'dataset/modified_data.net.xml'
new_sumocfg_path = 'dataset/modified_data.sumocfg'

# 最適なパラメータの取得
import pickle

# データを開く
with open("best_params.pickle", "rb") as f:
    best_params = pickle.load(f)


def extract_params(best_params):
    R_params = {eval(k[2:]): v for k, v in best_params.items() if k.startswith('R_')}
    beta = best_params.get('beta', None)
    return R_params, beta

R_params, beta = extract_params(best_params)

junc_info, edge_info = get_info(new_net_path, new_sumocfg_path)
lanes_from_state = extract_lanes_from_state(new_net_path)

B = make_B(edge_info)

print('信号付き交差点の数:', len(junc_info.keys()))

total_var=0
for junc, info in junc_info.items():
    total_var += info['num_modes']
print('変数の数:', total_var)
print('Rのキーの数:', len(R_params.keys()))

def run_simulation(
    B=None,
    R=None,
    alpha=None, beta=None, gamma=None,
    num_reads=None,
    Tall=None,
    interval=None,
    yellow_signal_time=None,
    lam=None,
    sampler=None,
    method=None,
    net_path=None,
    sumocfg_path=None,
    lanes_from_state=None,
    junc_info=None,
    is_visualize=False
    ):

    if method != 'fixed':
        BR = make_normalized_BR(B, R)

    if is_visualize:
        sumoBinary = "PATH_YOUR_SUMO-GUI"
    else:
        sumoBinary = "PATH_YOUR_SUMO"
    sumoCmd = [sumoBinary, "-c", sumocfg_path, "--no-warnings", "--log", "sumo.log"]
    traci.start(sumoCmd)

    step = -1
    yellow_junc = {}
    mode_log={}
    execution_time_log={} # [sec]
    num_car_on_map_log = {}
    vehicle_counts_log = {}
    while step < Tall:
        if step == -1:
            traci.simulationStep()
            step += 1
            continue

        vehicle_counts = count_vehicles(lanes_from_state, 1, net_path) # 右折に重み付けしない
        vehicle_counts_log[step] = vehicle_counts

        vehicle_id_list = traci.vehicle.getIDList()
        vehicle_count = len(vehicle_id_list)
        num_car_on_map_log[step] = vehicle_count
        
        # 信号切替時に黄色を挟む
        for junc, info in yellow_junc.items():
            if info['time'] == 0:
                traci.trafficlight.setRedYellowGreenState(junc, info['next_state'])
                yellow_junc[junc]['time'] -= 1
            if info['time'] > 0:
                yellow_junc[junc]['time'] -= 1

        if method == 'fixed':
            mode_log[step] = {}
            for junc in junc_info.keys():
                current_phase = traci.trafficlight.getPhase(junc)
                mode_log[step][junc] = current_phase

        if step % interval == 0 and method != 'fixed':
            # 最適化計算
            if method == 'sa':
                vehicle_counts = count_vehicles(lanes_from_state, lam, net_path) # 車の数をカウントする
                C = make_normalized_C(vehicle_counts, junc_info)
                lowest_dict, sampleset, elapsed_time = use_annealing(C, BR, alpha, beta, gamma, num_reads, sampler, junc_info)
                mode_dict = get_mode(lowest_dict)
                execution_time_log[step] =  elapsed_time # [s]
            if method == 'gurobi':
                vehicle_counts = count_vehicles(lanes_from_state, lam, net_path) # 車の数をカウントする
                C = make_normalized_C(vehicle_counts, junc_info)
                lowest_dict, runtime, elapsed_time = use_gurobi(C, BR, alpha, beta, gamma, junc_info)
                mode_dict = get_mode(lowest_dict)
                execution_time_log[step] = elapsed_time # [s]
            mode_log[step] = mode_dict


            # 信号の操作
            for junc, next_phase in mode_dict.items():
                traci.trafficlight.setProgram(junc, "0") # これが無いと挙動がおかしくなる
                current_phase = traci.trafficlight.getPhase(junc)
                current_state = traci.trafficlight.getRedYellowGreenState(junc)
                traci.trafficlight.setPhase(junc, next_phase)
                next_state = traci.trafficlight.getRedYellowGreenState(junc)
                # 切替時に黄色を挟む
                if current_phase != next_phase:
                    new_state = transition_state(current_state, next_state)
                    traci.trafficlight.setRedYellowGreenState(junc, new_state)
                    yellow_junc[junc] = {'time':yellow_signal_time, 'next_state':next_state} # 黄色を挟む時間

        traci.simulationStep()
        step += 1

    traci.close()
    total_waiting_time = calculate_total_waiting_time('dataset/tripinfo-out.xml')

    log = {'mode_log': mode_log, 'execution_time_log': execution_time_log, \
        'num_car_on_map_log': num_car_on_map_log, 'vehicle_counts_log': vehicle_counts_log}

    return total_waiting_time, log

# シミュレーションパラメータを辞書にまとめる
simulation_params = {
    'B': B, 
    'R': R_params,
    'alpha': alpha,
    'beta': 0,
    'gamma': gamma,
    'Tall': Tall,
    'interval': interval, # 最適化の感覚
    'yellow_signal_time': yellow_signal_time, # 黄色を挟む時間
    'lam': lam,  # 右折レーンの重み
    'method': 'gurobi',
    'net_path': new_net_path,
    'sumocfg_path': new_sumocfg_path,
    'lanes_from_state': lanes_from_state,
    'junc_info': junc_info,
    'is_visualize': False
}

result['local'] = {}

# シミュレーションを実行
total_waiting_time, log = run_simulation(**simulation_params)
print(f'Total waiting time: {total_waiting_time} [s]')
print(f'Total waiting time: {total_waiting_time/3600} [hour]')

result['local']['total_waiting_time'] = total_waiting_time
result['local']['mode_log'] = log['mode_log'] # [s]
result['local']['execution_time_log'] = log['execution_time_log'] # [s]
result['local']['num_car_on_map_log'] = log['num_car_on_map_log']

# シミュレーションパラメータを辞書にまとめる
simulation_params = {
    'B': B, 
    'R': R_params,
    'alpha': alpha,
    'beta': beta,
    'gamma': gamma,
    'Tall': Tall,
    'interval': interval, # 最適化の感覚
    'yellow_signal_time': yellow_signal_time, # 黄色を挟む時間
    'lam': lam,  # 右折レーンの重み
    'method': 'gurobi',
    'net_path': new_net_path,
    'sumocfg_path': new_sumocfg_path,
    'lanes_from_state': lanes_from_state,
    'junc_info': junc_info,
    'is_visualize': False
}

total_waiting_time_log={}
execution_time_log={}

result['gurobi'] = {}
for t in range(num_instances):
    result['gurobi']['iter'+str(t)]={}
    # シミュレーションを実行
    total_waiting_time, log = run_simulation(**simulation_params)
    print(f'Total waiting time: {total_waiting_time} [s]')

    result['gurobi']['iter'+str(t)]['total_waiting_time'] = total_waiting_time
    result['gurobi']['iter'+str(t)]['mode_log'] = log['mode_log'] # [s]
    result['gurobi']['iter'+str(t)]['execution_time_log'] = log['execution_time_log'] # [s]
    result['gurobi']['iter'+str(t)]['num_car_on_map_log'] = log['num_car_on_map_log']

# シミュレーションパラメータを辞書にまとめる
simulation_params = {
    'B': B, 
    'R': R_params,
    'alpha': alpha,
    'beta': beta,
    'gamma': gamma,
    'num_reads': 1000,
    'Tall': Tall,
    'interval': interval, # 最適化の感覚
    'yellow_signal_time': yellow_signal_time, # 黄色を挟む時間
    'lam': lam,  # 右折レーンの重み
    'sampler': SASampler(),
    'method': 'sa',
    'net_path': new_net_path,
    'sumocfg_path': new_sumocfg_path,
    'lanes_from_state': lanes_from_state,
    'junc_info': junc_info,
    'is_visualize': False
}


total_waiting_time_log={}
execution_time_log={}

result['sa'] = {}
for t in range(num_instances):
    result['sa']['iter'+str(t)]={}
    # シミュレーションを実行
    total_waiting_time, log = run_simulation(**simulation_params)
    print(f'Total waiting time: {total_waiting_time} [s]')
    result['sa']['iter'+str(t)]['total_waiting_time'] = total_waiting_time
    result['sa']['iter'+str(t)]['mode_log'] = log['mode_log'] # [s]
    result['sa']['iter'+str(t)]['execution_time_log'] = log['execution_time_log'] # [s]
    result['sa']['iter'+str(t)]['num_car_on_map_log'] = log['num_car_on_map_log'] # [s]


# シミュレーションパラメータを辞書にまとめる
simulation_params = {
    'Tall': Tall,
    'interval': interval,
    'method': 'fixed',
    'net_path': original_net_path,
    'sumocfg_path': original_sumocfg_path,
    'lanes_from_state': lanes_from_state,
    'junc_info': junc_info,
    'is_visualize': False
}

total_waiting_time_log={}
execution_time_log={}

result['fixed'] = {}

# シミュレーションを実行
total_waiting_time, log = run_simulation(**simulation_params)
print(f'Total waiting time: {total_waiting_time} [s]')

result['fixed']['total_waiting_time'] = total_waiting_time
result['fixed']['mode_log'] = log['mode_log'] # [s]
result['fixed']['execution_time_log'] = log['execution_time_log'] # [s]
result['fixed']['num_car_on_map_log'] = log['num_car_on_map_log'] # [s]

import pickle

# データを保存
with open("result.pickle", "wb") as f:
    pickle.dump(result, f)