{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Load the data\n", "data = pd.read_pickle(\"result.pickle\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Initialize empty dataframes\n", "waiting_time_df = pd.DataFrame()\n", "execution_time_df = pd.DataFrame()\n", "num_car_on_map_df = pd.DataFrame()\n", "\n", "# For 'fixed'\n", "waiting_time_df['fixed'] = [data['fixed']['total_waiting_time']]\n", "\n", "# For 'gurobi'\n", "gurobi_waiting_times = []\n", "gurobi_execution_times = pd.DataFrame()\n", "gurobi_num_car_on_map = pd.DataFrame()\n", "\n", "for iteration in data['gurobi'].keys():\n", "    gurobi_waiting_times.append(data['gurobi'][iteration]['total_waiting_time'])\n", "    gurobi_execution_times[iteration] = pd.Series(data['gurobi'][iteration]['execution_time_log'])\n", "    gurobi_num_car_on_map[iteration] = pd.Series(data['gurobi'][iteration]['num_car_on_map_log'])\n", "\n", "# For 'sa'\n", "sa_waiting_times = []\n", "sa_execution_times = pd.DataFrame()\n", "sa_num_car_on_map = pd.DataFrame()\n", "\n", "for iteration in data['sa'].keys():\n", "    sa_waiting_times.append(data['sa'][iteration]['total_waiting_time'])\n", "    sa_execution_times[iteration] = pd.Series(data['sa'][iteration]['execution_time_log'])\n", "    sa_num_car_on_map[iteration] = pd.Series(data['sa'][iteration]['num_car_on_map_log'])\n", "\n", "# For 'qa'\n", "qa_waiting_times = []\n", "qa_execution_times = pd.DataFrame()\n", "qa_num_car_on_map = pd.DataFrame()\n", "\n", "for iteration in data['qa'].keys():\n", "    qa_waiting_times.append(data['qa'][iteration]['total_waiting_time'])\n", "    qa_execution_times[iteration] = pd.Series(data['qa'][iteration]['execution_time_log'])\n", "    qa_num_car_on_map[iteration] = pd.Series(data['qa'][iteration]['num_car_on_map_log'])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>停止時間"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Calculate mean and standard deviation for 'sa' waiting times\n", "waiting_time_df['sa_mean'] = [pd.Series(sa_waiting_times).mean()]\n", "waiting_time_df['sa_std'] = [pd.Series(sa_waiting_times).std()]\n", "\n", "# Calculate mean and standard deviation for 'qa' waiting times\n", "waiting_time_df['qa_mean'] = [pd.Series(qa_waiting_times).mean()]\n", "waiting_time_df['qa_std'] = [pd.Series(qa_waiting_times).std()]\n", "\n", "# Calculate mean and standard deviation for 'gurobi' waiting times\n", "waiting_time_df['gurobi_mean'] = [pd.Series(gurobi_waiting_times).mean()]\n", "waiting_time_df['gurobi_std'] = [pd.Series(gurobi_waiting_times).std()]\n", "\n", "# Assuming waiting_time_df is already defined and has the same structure as before\n", "waiting_time_df_hour = waiting_time_df.copy()\n", "waiting_time_df_hour[['gurobi_mean', 'gurobi_std', 'sa_mean', 'sa_std', 'qa_mean', 'qa_std','fixed']] /= 3600  # convert from seconds to hours"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Change font size\n", "plt.rcParams.update({'font.size': 18})\n", "\n", "# Define colors for the bar plot\n", "colors = ['skyblue', 'lightgreen', 'salmon', 'orange', 'green']\n", "\n", "\n", "# Plot total_waiting_time\n", "plt.figure(figsize=(10, 6))\n", "methods = ['gurobi', 'sa', 'qa', 'local', 'fixed']\n", "\n", "for i, method in enumerate(methods):\n", "    if method == 'gurobi':\n", "        method='<PERSON><PERSON><PERSON>'\n", "        # plt.bar(method, waiting_time_df_hour['gurobi_mean'], yerr=waiting_time_df_hour['gurobi_std'], capsize=10, color=colors[i], alpha=0.6)\n", "        plt.bar(method, waiting_time_df_hour['gurobi_mean'], color=colors[i], alpha=0.6)\n", "    elif method == 'sa':\n", "        method='SA'\n", "        plt.bar(method, waiting_time_df_hour['sa_mean'], yerr=waiting_time_df_hour['sa_std'], capsize=10, color=colors[i], alpha=0.6)\n", "    elif method == 'qa':\n", "        method='QA'\n", "        plt.bar(method, waiting_time_df_hour['qa_mean'], yerr=waiting_time_df_hour['qa_std'], capsize=10, color=colors[i], alpha=0.6)\n", "    elif method == 'local':\n", "        method='Local'\n", "        plt.bar(method, data['local']['total_waiting_time']/3600, color=colors[i], alpha=0.6)\n", "    else:\n", "        plt.bar('Fixed', waiting_time_df_hour.loc[0, method], color=colors[i], alpha=0.6)\n", "\n", "plt.axhline(data['local']['total_waiting_time'] / 3600, color='gray', linestyle='--', linewidth=1)  # Add dotted horizontal line at 'local' value\n", "plt.ylabel('Total Waiting Time [hour]', fontsize=16)\n", "plt.savefig('waiting_time.pdf', format='pdf')\n", "plt.show()\n", "plt.close()  # Close the plot to prevent it from being displayed again\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>計算時間"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Transpose execution_time_df for plotting\n", "execution_time_df = execution_time_df.transpose()\n", "\n", "gurobi_execution_mean = gurobi_execution_times.mean(axis=1)\n", "gurobi_execution_std = gurobi_execution_times.std(axis=1)\n", "\n", "sa_execution_mean = sa_execution_times.mean(axis=1)\n", "sa_execution_std = sa_execution_times.std(axis=1)\n", "\n", "qa_execution_mean = qa_execution_times.mean(axis=1)\n", "qa_execution_std = qa_execution_times.std(axis=1)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x504 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.rcParams.update({'font.size': 18})\n", "\n", "# Plot execution_time_log\n", "plt.figure(figsize=(12, 7))\n", "\n", "# plot gurobi and sa data\n", "plt.errorbar(gurobi_execution_mean.index, gurobi_execution_mean, yerr=gurobi_execution_std, label='Gurobi', linewidth=2.0, marker='o', linestyle='', markersize=7, capsize=5)\n", "plt.errorbar(sa_execution_mean.index, sa_execution_mean, yerr=sa_execution_std, label='SA', linewidth=2.0, marker='s', linestyle='', markersize=7, capsize=5)\n", "plt.errorbar(qa_execution_mean.index, qa_execution_mean, yerr=qa_execution_std, label='QA', linewidth=2.0, marker='^', linestyle='', markersize=7, capsize=5)\n", "\n", "# set the x-ticks to match the data points\n", "all_ticks = list(set(execution_time_df.columns).union(set(sa_execution_mean.index)))\n", "plt.xticks(ticks=all_ticks, labels=all_ticks)\n", "\n", "# set the x-ticks with a step size of 40\n", "min_tick = min(all_ticks)\n", "max_tick = max(all_ticks)\n", "step_size = 40\n", "ticks_40_step = np.arange(min_tick, max_tick + step_size, step_size)\n", "\n", "plt.xticks(ticks=ticks_40_step, labels=ticks_40_step)\n", "\n", "plt.xlabel('Time Step [sec]')\n", "plt.ylabel('Calculation Time [sec]')\n", "plt.legend()\n", "plt.savefig('calc_time.pdf', format='pdf')\n", "plt.show()\n", "plt.close()  # Close the plot to prevent it from being displayed again\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>車の数"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["fixed_num_car_on_map = pd.Series(data['fixed']['num_car_on_map_log'])\n", "\n", "gurobi_num_car_on_map_mean = gurobi_num_car_on_map.mean(axis=1)\n", "gurobi_num_car_on_map_std = gurobi_num_car_on_map.std(axis=1)\n", "\n", "sa_num_car_on_map_mean = sa_num_car_on_map.mean(axis=1)\n", "sa_num_car_on_map_std = sa_num_car_on_map.std(axis=1)\n", "\n", "qa_num_car_on_map_mean = qa_num_car_on_map.mean(axis=1)\n", "qa_num_car_on_map_std = qa_num_car_on_map.std(axis=1)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 864x504 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.axes_grid1.inset_locator import inset_axes, mark_inset\n", "\n", "plt.rcParams.update({'font.size': 18})\n", "\n", "# 定義: ポイント間隔\n", "point_interval = 20\n", "\n", "# Main figure\n", "fig, main_ax = plt.subplots(figsize=(12, 7))\n", "\n", "# Plot data on the main axes\n", "main_ax.plot(fixed_num_car_on_map.index[::point_interval], fixed_num_car_on_map[::point_interval], label='Fixed', linestyle='', linewidth=2.0, marker=\"D\", markersize=7)\n", "# main_ax.plot(gurobi_num_car_on_map_mean.index[::point_interval], gurobi_num_car_on_map_mean.index[::point_interval], label='Gurobi', linestyle='', linewidth=2.0, marker=\"o\", markersize=7)\n", "main_ax.errorbar(gurobi_num_car_on_map_mean.index[::point_interval], gurobi_num_car_on_map_mean.iloc[::point_interval], label='Gurobi', linewidth=2.0, linestyle='', marker='o', markersize=7, capsize=5)\n", "# main_ax.errorbar(gurobi_num_car_on_map_mean.index[::point_interval], gurobi_num_car_on_map_mean.iloc[::point_interval], yerr=gurobi_num_car_on_map_std.iloc[::point_interval], label='<PERSON>urobi', linewidth=2.0, linestyle='', marker='o', markersize=7, capsize=5)\n", "main_ax.errorbar(sa_num_car_on_map_mean.index[::point_interval], sa_num_car_on_map_mean.iloc[::point_interval], yerr=sa_num_car_on_map_std.iloc[::point_interval], label='SA', linewidth=2.0, linestyle='', marker='s', markersize=7, capsize=5)\n", "main_ax.errorbar(qa_num_car_on_map_mean.index[::point_interval], qa_num_car_on_map_mean.iloc[::point_interval], yerr=qa_num_car_on_map_std.iloc[::point_interval], label='QA', linewidth=2.0, linestyle='', marker='^', markersize=7, capsize=5)\n", "\n", "# Set x-ticks and y-ticks for main axes\n", "xticks = np.arange(0, 401, 100)\n", "main_ax.set_xticks(xticks)\n", "\n", "yticks = np.arange(0, 1001, 200)\n", "main_ax.set_yticks(yticks)\n", "\n", "main_ax.set_xlabel('Time Step [sec]')\n", "main_ax.set_ylabel('Number of Vehicles on Map')\n", "main_ax.legend()\n", "\n", "# Create an inset axes with loc set to 'center'\n", "axins = inset_axes(main_ax, width='150%', height='300%', loc='center', bbox_to_anchor=(0.3, 0.25, 0.1, 0.2), bbox_transform=main_ax.transAxes)\n", "\n", "\n", "# Plot the same data on the inset\n", "axins.plot(fixed_num_car_on_map.index[::point_interval], fixed_num_car_on_map[::point_interval], linestyle='', linewidth=2.0, marker=\"D\", markersize=7)\n", "# axins.plot(gurobi_num_car_on_map_mean.index[::point_interval], gurobi_num_car_on_map_mean.index[::point_interval], linestyle='', linewidth=2.0, marker=\"o\", markersize=7)\n", "# axins.errorbar(gurobi_num_car_on_map_mean.index[::point_interval], gurobi_num_car_on_map_mean.iloc[::point_interval], yerr=gurobi_num_car_on_map_std.iloc[::point_interval], linewidth=2.0, linestyle='', marker='o', markersize=7, capsize=5)\n", "axins.errorbar(gurobi_num_car_on_map_mean.index[::point_interval], gurobi_num_car_on_map_mean.iloc[::point_interval], linewidth=2.0, linestyle='', marker='o', markersize=7, capsize=5)\n", "axins.errorbar(sa_num_car_on_map_mean.index[::point_interval], sa_num_car_on_map_mean.iloc[::point_interval], yerr=sa_num_car_on_map_std.iloc[::point_interval], linewidth=2.0, linestyle='', marker='s', markersize=7, capsize=5)\n", "axins.errorbar(qa_num_car_on_map_mean.index[::point_interval], qa_num_car_on_map_mean.iloc[::point_interval], yerr=qa_num_car_on_map_std.iloc[::point_interval], linewidth=2.0, linestyle='', marker='^', markersize=7, capsize=5)\n", "\n", "# Define the range for the inset axes\n", "x1, x2 = 210, 230  # x-range\n", "y1, y2 = 680, 735  # y-range\n", "axins.set_xlim(x1, x2)\n", "axins.set_ylim(y1, y2)\n", "axins.set_xticks(np.arange(200, x2, 100))\n", "axins.set_yticks(np.arange(900, y2, 100))\n", "\n", "# Add the inset indicator lines\n", "main_ax.indicate_inset_zoom(axins)\n", "mark_inset(main_ax, axins, loc1=2, loc2=4, fc=\"none\", ec=\"0.5\")  # loc1, loc2はコネクタの位置を指定するためのパラメータ\n", "\n", "\n", "plt.savefig('num_cars.pdf', format='pdf')\n", "plt.show()\n", "plt.close()\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 最適なパラメータの取得\n", "import pickle\n", "\n", "# データを開く\n", "with open(\"best_params.pickle\", \"rb\") as f:\n", "    best_params = pickle.load(f)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def extract_params(best_params):\n", "    R_params = {eval(k[2:]): v for k, v in best_params.items() if k.startswith('R_')}\n", "    beta = best_params.get('beta', None)\n", "    return R_params, beta\n", "\n", "R_params, beta = extract_params(best_params)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>パラメータ"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 720x720 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# 与えられたデータ\n", "data = R_params.copy()\n", "\n", "# x1とy1のユニークな値を取得\n", "unique_x1_values = sorted(list(set(k[0] for k in data.keys())))\n", "unique_y1_values = sorted(list(set(k[2] for k in data.keys())))\n", "\n", "# ラベル変換のマッピングを作成\n", "x1_label_mapping = {value: i for i, value in enumerate(unique_x1_values)}\n", "y1_label_mapping = {value: i for i, value in enumerate(unique_y1_values)}\n", "\n", "# x軸とy軸の座標を抽出し、変換されたラベルを使用\n", "x_coords = [(x1_label_mapping[k[0]], k[1]) for k in data.keys()]\n", "y_coords = [(y1_label_mapping[k[2]], k[3]) for k in data.keys()]\n", "\n", "# ユニークなx座標とy座標を取得\n", "unique_x_coords = sorted(list(set(x_coords)))\n", "unique_y_coords = sorted(list(set(y_coords)))\n", "\n", "num_x = len(unique_x_coords)\n", "num_y = len(unique_y_coords)\n", "\n", "# 値が存在しない場合のデフォルト値をNaNに設定\n", "matrix = np.full((num_y, num_x), np.nan)\n", "\n", "for (x1, x2, y1, y2), value in data.items():\n", "    x_index = unique_x_coords.index((x1_label_mapping[x1], x2))\n", "    y_index = unique_y_coords.index((y1_label_mapping[y1], y2))\n", "    matrix[y_index, x_index] = value\n", "\n", "plt.figure(figsize=(10, 10))\n", "img = plt.imshow(matrix, cmap='coolwarm', interpolation='nearest')\n", "cbar = plt.colorbar(img, shrink=0.6)  # shrinkパラメータでカラーバーのサイズを調整\n", "\n", "# カラーバーのティックを1, 0, -1に設定\n", "cbar.set_ticks([1, 0, -1])\n", "cbar.set_ticklabels(['1', '0', '-1'])\n", "cbar.set_label('$R_{im, jn}$', rotation=270, labelpad=20, fontsize=24)  # タイトルを追加\n", "\n", "# タイトルとサブタイトルを設定\n", "# plt.title('Optimized parameters', fontsize=16, fontweight='bold')\n", "\n", "\n", "# fontsizeを追加してフォントサイズを調整\n", "plt.xticks(np.arange(num_x), unique_x_coords, rotation=90, fontsize=10)\n", "plt.yticks(np.arange(num_y), unique_y_coords, fontsize=10)\n", "\n", "# 軸のラベルを設定\n", "plt.xlabel('( $i, m$ )', fontsize=24)\n", "plt.ylabel('( $j , n$ )', fontsize=24)\n", "\n", "plt.tight_layout()\n", "plt.savefig('optimized_R.pdf', format='pdf')\n", "plt.show()\n", "plt.close()  # Close the plot to prevent it from being displayed again\n"]}], "metadata": {"kernelspec": {"display_name": "opt", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}